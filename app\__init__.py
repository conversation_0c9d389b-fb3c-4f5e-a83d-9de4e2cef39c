from flask import Flask
from asgiref.wsgi import WsgiToAsgi
from config import Config
from extensions import cors # Comment this if not using Flask to manage CORS
from app.core.nlp_models import init_models

def create_app(config_class=Config):
    """Application factory function."""
    app = Flask(__name__, instance_relative_config=True)
    
    # Load default config
    app.config.from_object(config_class)
    
    # Load instance config if it exists
    try:
        app.config.from_pyfile('config.py')
    except FileNotFoundError:
        pass
    
    # Initialize extensions
    cors.init_app(app) # Comment this if not using Flask to manage CORS
    
    # Initialize models and attach to app
    with app.app_context():
        app.embeddings_manager, app.nlp_spacy, app.label_pool_data, app.label_pool_name_set = init_models(app)
    
    # Register blueprints
    from app.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    asgi_app = WsgiToAsgi(app)
    return asgi_app