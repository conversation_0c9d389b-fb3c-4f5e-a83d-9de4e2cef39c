from flask import jsonify, request
from app.api import bp
from flask import current_app
from app.core import labels_generator_llm_st, labels_generator_textrank_st

@bp.route("/generate_tags", methods=["POST"])
def generate_tags():
    data = request.json
    
    if not data or "items" not in data:
        return jsonify({"error": "Invalid input. 'items' key is required."}), 400

    items = data["items"]
    model = data.get("model", "gpt-4o-mini")
    pipeline = 1 if model == "pytextrank" else 2

    results = []
    for item in items:
        index = item.get("index", "")
        item_id = item.get("key", "")
        title = item.get("title", "")
        abstract = item.get("abstract", "")

        search_result = {}
        if pipeline == 1:
            search_result = labels_generator_textrank_st(
                current_app.embeddings_manager,
                current_app.nlp_spacy,
                title, 
                abstract, 
                collection_name="default_collection",
                debug=True
            )
        elif pipeline == 2:
            search_result = labels_generator_llm_st(
                current_app.embeddings_manager,
                current_app.label_pool_name_set,
                title, 
                abstract, 
                collection_name="default_collection",
                llm_model_name=model,
                debug=True
            )

        results.append({
            "index": index,
            "key": item_id,
            "title": title,
            "tags": {
                "matched_tags": search_result.get("matched_labels", ""),
                "concept_tags": search_result.get("keywords", ""),
                "person_org_tags": search_result.get("persons_organizations", ""),
                "time_place_tags": search_result.get("times_places", "")
            }
        })

    return jsonify(results)

@bp.route("/generate_tags_test", methods=["POST"])
def test_tags():
    data = request.json

    if not data or "items" not in data:
        return jsonify({"error": "Invalid input. 'items' key is required."}), 400

    items = data["items"]
    results = []
    for item in items:
        index = item.get("index", "")
        item_id = item.get("key", "")
        title = item.get("title", "")
        abstract = item.get("abstract", "")

        results.append({
            "index": index,
            "key": item_id,
            "title": title,
            "tags": {
                "matched_tags": ["example_tag1", "example_tag2"],
                "concept_tags": ["concept_example1", "concept_example2"],
                "person_org_tags": ["person_example1", "organization_example1"],
                "time_place_tags": ["time_example1", "place_example1", "Localism"]
            }
        })

    return jsonify(results)

@bp.route("/tags/all", methods=["GET"])
def get_all_tags():
    try:
        # return jsonify(current_app.embeddings_manager.query(collection_name="default_collection", filter="label_id >= 0", output_fields=["name","category", "Record_ID"])), 200
        return jsonify(current_app.label_pool_data), 200
    except Exception as e:
        return jsonify({"error": "An error occurred while fetching tags.", "details": str(e)}), 500

@bp.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "API is running"}), 200