from typing import List, Dict, Optional
from math import ceil
from pymilvus import MilvusClient, DataType, model

import os
from .csv_converter import csv_to_json

class EmbeddingManager:
    def __init__(self, milvus_uri: str = "./milvus.db", embedding_model: str = "all-MiniLM-L6-v2", 
                 openai_api_key: Optional[str] = None):
        """
        Initialize the EmbeddingManager.

        :param milvus_uri: Milvus uri.
        :param embedding_model: Name of the Sentence Transformer model or 'openai' for OpenAI embeddings.
        :param openai_api_key: OpenAI API key if using OpenAI embeddings.
        """
        self.milvus_client = MilvusClient(uri=milvus_uri)
        self.embedding_model = embedding_model
        self.openai_api_key = openai_api_key

        if self.embedding_model.startswith("text-embedding") and self.openai_api_key:
            self.embedding_ef = model.dense.OpenAIEmbeddingFunction(
                model_name=self.embedding_model, # Specify the model name
                api_key=self.openai_api_key, # Provide your OpenAI API key
                dimensions=1536 # Set the embedding dimensionality
            )
        else:
            # print("OpenAI API key not provided. Using SentenceTransformer embeddings.")
            self.embedding_ef = model.dense.SentenceTransformerEmbeddingFunction(
                model_name=self.embedding_model, # Specify the model name
                device='cpu' # Specify the device to use, e.g., 'cpu' or 'cuda:0'
            )

        
    def create_collection(self, collection_name: str = "default"):
        """
        Create a Milvus collection with dynamic schema based on the embedding dimension.

        :param collection_name: Name of the collection.
        """
        if self.milvus_client.has_collection(collection_name):
            print(f"Collection '{collection_name}' already exists.")
            return "exists"

        # Calculate embedding dimension dynamically
        embedding_dimension = len(self.embedding_ef(['sample_text'])[0])

        # Define the schema dynamically
        schema = self.milvus_client.create_schema(
            enable_dynamic_field=True,
        )
        schema.add_field(field_name="label_id", datatype=DataType.INT64, is_primary=True)
        schema.add_field(field_name="name", datatype=DataType.VARCHAR, max_length=512)
        schema.add_field(field_name="embedding", datatype=DataType.FLOAT_VECTOR, dim=embedding_dimension)
        schema.add_field(field_name="category", datatype=DataType.VARCHAR, default_value="general", max_length=512)
        
        index_params = self.milvus_client.prepare_index_params()
        index_params.add_index(
            field_name="embedding", 
            index_type="AUTOINDEX",
            metric_type="COSINE"
        )
        
        self.milvus_client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)
        print(f"Collection '{collection_name}' created successfully with embedding dimension {embedding_dimension}.")
        return "success"

    def upsert_data(self, collection_name: str, data: List[Dict]):
        """
        Insert data into the Milvus collection dynamically in batches.

        :param collection_name: Name of the collection.
        :param data: List of dictionaries containing the data.
        """
        batch_size = 10000

        # Split data into batches
        num_batches = ceil(len(data) / batch_size)

        for batch_index in range(num_batches):
            # Get the current batch of data
            start_idx = batch_index * batch_size
            end_idx = min((batch_index + 1) * batch_size, len(data))
            batch_data = data[start_idx:end_idx]

            # Extract texts and generate embeddings for the current batch
            texts = [item["name"] for item in batch_data]
            embeddings = self.embedding_ef(texts)

            # Add embeddings to the batch data
            for index, item in enumerate(batch_data):
                item["embedding"] = embeddings[index]

            # Perform upsert for the current batch
            self.milvus_client.upsert(collection_name=collection_name, data=batch_data)
            print(f"Upserted {len(batch_data)} records into collection '{collection_name}' (Batch {batch_index + 1}/{num_batches}).")

        print(self.milvus_client.describe_collection(collection_name=collection_name))

    
    def load_collection(self, collection_name: str, load_fields=["label_id", "name", "embedding", "category"], skip_load_dynamic_field=True):
        """
        Load the specified Milvus collection.

        :param collection_name: Name of the collection.
        """
        self.milvus_client.load_collection(collection_name=collection_name, load_fields=load_fields, skip_load_dynamic_field=skip_load_dynamic_field)

    def search(self, collection_name: str, query_texts: List[str], top_k: int = 3, simple_output: bool = True, filter: Optional[str] = None, output_fields: Optional[List[str]] = ["name"], search_params: Optional[Dict] = {"metric_type": "COSINE"}):
        """
        Search for similar texts in the Milvus collection.

        :param collection_name: Name of the collection.
        :param query_texts: a list of texts to search for.
        :param top_k: Number of top results to return.
        :param simple_output: If True, return a simplified list of unique labels. The output_fields argument may be suppressed but must at least has "name". If not, the original search results from milvus_client are returned.
        :param filter: Optional filter expression.
        :param output_fields: Optional list of fields to return. The primary field will be returned if not set.
        :return: List of search results.
        """
        query_embeddings = self.embedding_ef(query_texts)
        # output_fields = ["Record_Type"] if output_fields is None else output_fields

        results = self.milvus_client.search(collection_name=collection_name, data=query_embeddings, limit=top_k, filter=filter, output_fields=output_fields, search_params=search_params)

        if simple_output:
            label_set = set()
            for sublist in results:
                for item in sublist:
                    if 'entity' in item and 'name' in item['entity']:
                        label_set.add(item['entity']['name'])
            return list(label_set)
        else:
            return results
    
    def query(self, collection_name, filter, output_fields):
        return self.milvus_client.query(collection_name=collection_name, filter=filter, output_fields=output_fields)

    def delete_collection(self, collection_name: str):
        """
        Delete a Milvus collection.

        :param collection_name: Name of the collection.
        """
        if self.milvus_client.has_collection(collection_name):
            self.milvus_client.drop_collection(collection_name)
            print(f"Collection '{collection_name}' deleted successfully.")
        else:
            print(f"Collection '{collection_name}' does not exist.")


# Example usage
if __name__ == "__main__":
    # Initialize the EmbeddingManager
    embedding_manager = EmbeddingManager(embedding_model="all-MiniLM-L6-v2")

    current_directory = os.path.dirname(os.path.abspath(__file__))
    label_pool_path = os.path.join(current_directory, 'label_pool.csv')
    has_header_input = input("Does the CSV file have a header? (yes/no): ").strip().lower()
    has_header = has_header_input == 'yes'
    json_data = csv_to_json(label_pool_path, delimiter='#', has_header=has_header)
    # print("JSON Data:\n", json_data)

    # Create a collection and upsert data
    res = embedding_manager.create_collection("test_collection")
    if res == "success":
        embedding_manager.upsert_data("test_collection", json_data)

    # Search
    results = embedding_manager.search("test_collection", ["industrial revolution", "Japanese empire"], top_k=3, simple_output=True, filter='category == "Concept"', search_params={"metric_type": "COSINE", "params":{"radius": 0.2}})
    
    print("Search Results:", results)

    # # Delete the collection
    # embedding_manager.delete_collection("test_collection")