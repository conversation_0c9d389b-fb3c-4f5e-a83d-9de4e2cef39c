import spacy
import os
import time
import pandas as pd
from .embeddings_management import EmbeddingManager
from .csv_converter import csv_to_json

def init_models(app):
    """Initialize language models and the default label pool's vector database."""
    start = time.perf_counter()
    # Load Sentence Transformer model and Spacy NLP model
    current_directory = os.path.dirname(os.path.abspath(__file__))
    milvus_db_path = os.path.join(current_directory, "milvus.db")
    embeddings_manager = EmbeddingManager(milvus_uri=milvus_db_path, embedding_model=app.config['ST_MODEL_NAME'])
    nlp_spacy = spacy.load(app.config['NLP_SPACY_NAME'])
    end = time.perf_counter()
    print(f"Time for initializing language models: {end - start:.4f} seconds")
    
    start = time.perf_counter()
    print("Initializing label pool...")
    # Read label data from csv file and convert to json
    label_pool_path = os.path.join(current_directory, app.config['LABEL_POOL_FILE'])
    label_pool_data = csv_to_json(label_pool_path, delimiter='|', has_header='yes')
    label_pool_data_df = pd.DataFrame(label_pool_data)
    label_pool_name_set = set(label_pool_data_df['name'])

    # Create a collection, upsert data if the collection was newly created, and load the collection into memory
    res = embeddings_manager.create_collection(collection_name=app.config['DEFAULT_COLLECTION_NAME'])
    if res == "success":
        print("Database and collection created. Started to compute and upsert embeddings.")
        embeddings_manager.upsert_data(collection_name=app.config['DEFAULT_COLLECTION_NAME'], data=label_pool_data)
    embeddings_manager.load_collection(collection_name=app.config['DEFAULT_COLLECTION_NAME'], load_fields=["label_id", "name", "embedding", "category"], skip_load_dynamic_field=True)
    print("Label pool and its embeddings initialized.")

    end = time.perf_counter()
    print(f"Time for initialization: {end - start:.4f} seconds")

    return embeddings_manager, nlp_spacy, label_pool_data, label_pool_name_set