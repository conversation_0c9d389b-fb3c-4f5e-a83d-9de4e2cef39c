import os

class Config:
    """Base config."""
    APP_ROOT = os.path.dirname(os.path.abspath(__file__))
    CORE_DIR = os.path.join(APP_ROOT, 'app', 'core')

    ST_MODEL_NAME = "all-mpnet-base-v2"
    NLP_SPACY_NAME = "en_core_web_md"
    LABEL_POOL_FILE = "default_label_pool.csv"

class ProductionConfig(Config):
    """Production config."""
    DEFAULT_COLLECTION_NAME = "default_collection"
    FLASK_ENV = 'production'
    DEBUG = False
    TESTING = False

class DevelopmentConfig(Config):
    """Development config."""
    DEFAULT_COLLECTION_NAME = "test_collection"
    FLASK_ENV = 'development'
    DEBUG = True
    TESTING = False

class TestingConfig(Config):
    """Testing config."""
    DEFAULT_COLLECTION_NAME = "test_collection"
    FLASK_ENV = 'testing'
    DEBUG = True
    TESTING = True